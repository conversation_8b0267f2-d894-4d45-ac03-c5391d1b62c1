# Hourglass

A sophisticated streaming payment system built on Hedera Smart Contracts using Solidity. Hourglass enables continuous token streaming with precise control over payment schedules, debt management, and NFT-based stream ownership.

## Features

### Core Functionality

- **Token Streaming**: Create continuous payment streams with customizable rates and time periods
- **NFT Ownership**: Each stream is represented as an ERC721 NFT for easy transferability
- **Debt Management**: Automatic handling of payment debts when funds are insufficient
- **Flexible Withdrawals**: Stream recipients can withdraw funds at any time
- **Multi-Token Support**: Support for any ERC20 token

### Advanced Features

- **Stream Modification**: Adjust stream rates and end times
- **Whitelisting**: Control who can withdraw from streams
- **Redirects**: Redirect stream payments to different addresses
- **Payer Controls**: Comprehensive controls for stream creators
- **Real-time Updates**: Automatic balance updates and stream status tracking

## Architecture

### Core Contracts

#### HourGlassPayer

The main contract that handles:

- Token deposits and withdrawals
- Stream creation and management
- Debt calculation and repayment
- NFT minting for stream ownership

#### HourGlassFactory

The factory contract that:

- Deploys new HourGlassPayer instances using CREATE2
- Predicts contract addresses for users
- Manages the registry of deployed payer contracts

#### Key Components

- **Token Vault**: Secure storage for deposited tokens
- **Stream Registry**: Tracks all active and inactive streams
- **Debt Tracking**: Monitors outstanding payments
- **Whitelist Management**: Controls access to stream withdrawals

## Getting Started

### Prerequisites

- [Foundry](https://book.getfoundry.sh/getting-started/installation.html)
- [Git](https://git-scm.com/)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/NaDasai/Hourglass.git
cd Hourglass/contracts
```

2. Install dependencies:

```bash
forge install
```

3. Build the contracts:

```bash
forge build
```

4. Run tests:

```bash
forge test
```

### Deployment

#### Local Development

```bash
# Start local Anvil node
anvil

# Deploy Factory contract to local network
forge script script/DeployFactory.s.sol --broadcast --fork-url http://localhost:8545

# Deploy HourGlass contract to local network
forge script script/DeployHourGlass.s.sol --broadcast --fork-url http://localhost:8545
```

#### Hedera Testnet

```bash
# Deploy Factory to Hedera testnet
forge script script/DeployFactory.s.sol --broadcast --rpc-url hedera_testnet

# Deploy HourGlass to Hedera testnet
forge script script/DeployHourGlass.s.sol --broadcast --rpc-url hedera_testnet
```

#### Hedera Mainnet

```bash
# Deploy Factory to Hedera mainnet
forge script script/DeployFactory.s.sol --broadcast --rpc-url hedera_mainnet

# Deploy HourGlass to Hedera mainnet
forge script script/DeployHourGlass.s.sol --broadcast --rpc-url hedera_mainnet
```

## Usage

### Creating a Stream

```solidity
// Example: Create a stream paying 1 token per second for 30 days
uint256 streamId = hourglass.createStream(
    tokenAddress,      // ERC20 token address
    recipientAddress,  // Stream recipient
    1000000000000000000, // 1 token per second (20 decimals)
    uint48(block.timestamp),     // Start time
    uint48(block.timestamp + 30 days) // End time
);
```

### Depositing Tokens

```solidity
// Deposit tokens into the vault
hourglass.deposit(tokenAddress, amount);
```

### Withdrawing from Stream

```solidity
// Withdraw available funds from stream
hourglass.withdrawAll(streamId);
```

## Contract Functions

### Stream Management

- `createStream()` - Create a new payment stream
- `modifyStream()` - Change stream parameters
- `stopStream()` - Pause a stream
- `resumeStream()` - Resume a paused stream
- `burnStream()` - Permanently end a stream

### Token Operations

- `deposit()` - Add tokens to the vault
- `withdrawPayer()` - Withdraw unused tokens (owner only)
- `withdraw()` - Withdraw from a specific stream
- `withdrawAll()` - Withdraw all available funds from a stream

### View Functions

- `withdrawable()` - Check available funds in a stream
- `streams()` - Get stream details
- `tokens()` - Get token balance information

## Security Features

- **Reentrancy Protection**: Uses OpenZeppelin's ReentrancyGuard
- **Safe Token Transfers**: Implements SafeERC20 for secure transfers
- **Access Control**: Owner-only functions for critical operations
- **Debt Prevention**: Automatic debt tracking and management
- **Input Validation**: Comprehensive validation of all parameters

## Testing

Run the full test suite:

```bash
forge test
```

Run tests with gas reporting:

```bash
forge test --gas-report
```

Run specific test file:

```bash
forge test --match-path test/Counter.t.sol
```

## Development

### Code Formatting

```bash
forge fmt
```

### Gas Snapshots

```bash
forge snapshot
```

### Coverage Report

```bash
forge coverage
```

## Project Structure

```
Streamera/
├── README.md                    # Project overview and documentation
├── contracts/                   # Foundry project directory
│   ├── README.md               # Contracts-specific documentation
│   ├── foundry.toml            # Foundry configuration
│   ├── foundry.lock            # Dependency lock file
│   ├── remappings.txt          # Import remappings
│   ├── cmds.md                 # Command documentation
│   ├── src/                    # Source contracts
│   │   ├── HourGlass.sol       # Main HourGlassPayer contract
│   │   ├── HourGlassFactory.sol # Factory for deploying HourGlass contracts
│   │   ├── Counter.sol         # Example counter contract
│   │   ├── interfaces/         # Contract interfaces
│   │   │   └── IHederaTokenService.sol
│   │   └── libraries/          # Utility libraries
│   │       ├── AssociateHelper.sol
│   │       └── TransferHelper.sol
│   ├── test/                   # Test files
│   │   └── Counter.t.sol       # Counter contract tests
│   ├── script/                 # Deployment scripts
│   │   ├── Counter.s.sol       # Counter deployment script
│   │   ├── DeployFactory.s.sol # Factory deployment script
│   │   └── DeployHourGlass.s.sol # HourGlass deployment script
│   ├── lib/                    # External libraries
│   │   ├── forge-std/          # Foundry standard library
│   │   └── openzeppelin-contracts/ # OpenZeppelin contracts
│   ├── broadcast/              # Deployment broadcast logs
│   └── cache/                  # Foundry cache
```

## Dependencies

- **OpenZeppelin Contracts**: ^5.0.0 - Industry-standard smart contract libraries
- **Forge Standard Library**: Latest - Testing and development utilities

## Network Support

- **Hedera Testnet**: For development and testing
- **Hedera Mainnet**: For production deployment
- **Local Anvil**: For local development

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions and support:

- Open an issue on GitHub
- Check the [Foundry documentation](https://book.getfoundry.sh/)
- Review [Hedera documentation](https://docs.hedera.com/)

---

Built with ❤️ using [Foundry](https://book.getfoundry.sh/) on [Hedera](https://hedera.com/)</content>
