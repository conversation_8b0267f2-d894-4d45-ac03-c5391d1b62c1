# Streamera

A sophisticated streaming payment system built on Hedera Smart Contracts using Solidity. Streamera enables continuous token streaming with precise control over payment rates, debt management, and NFT-based stream ownership.

## Features

### Core Functionality

- **Token Streaming**: Create continuous payment streams with customizable rates per second
- **NFT Ownership**: Each stream is represented as an ERC721 NFT for easy transferability and ownership tracking
- **Debt Management**: Automatic handling of payment debts with real-time balance calculations
- **Flexible Withdrawals**: Stream recipients can withdraw available funds at any time
- **Multi-Token Support**: Support for any ERC20 token with automatic token association on Hedera
- **Protocol Fees**: Configurable protocol fees with revenue collection mechanisms

### Advanced Features

- **Stream Lifecycle Management**: Create, deposit, withdraw, pause, refund, and void streams
- **Batch Operations**: Execute multiple operations in a single transaction for gas efficiency
- **Precise Rate Control**: Use fixed-point arithmetic (UD21x18) for accurate rate-per-second calculations
- **Security Features**: Reentrancy protection, delegate call prevention, and comprehensive access controls
- **Real-time Calculations**: Dynamic debt and withdrawable amount calculations based on elapsed time
- **Native Fee Collection**: Support for collecting native HBAR fees alongside token operations

## Architecture

### Core Contract

#### Streamera

The main contract that handles all streaming operations:

- **Stream Creation**: Create new payment streams with specified rates and recipients
- **Token Management**: Deposit tokens into streams and manage aggregate balances
- **Withdrawal Operations**: Process withdrawals with automatic fee calculations
- **Stream Controls**: Pause, refund, and void operations for stream management
- **NFT Integration**: ERC721 implementation for stream ownership and transferability
- **Protocol Administration**: Fee management and revenue collection

#### Key Components

- **Stream Registry**: Tracks all streams using a mapping of stream IDs to Stream structs
- **Aggregate Balance Tracking**: Monitors total token balances across all streams
- **Protocol Fee System**: Configurable fees per token with automatic revenue collection
- **Token Association**: Hedera-specific token association management
- **Debt Calculation Engine**: Real-time debt and balance calculations using precise mathematics

## System Architecture

### Data Structures

#### Stream Structure
Each stream is represented by a `DataTypes.Stream` struct containing:
- **balance**: Current token balance available in the stream
- **ratePerSecond**: Payment rate using UD21x18 fixed-point arithmetic
- **sender**: Address that created and funds the stream
- **recipient**: Address that receives payments (NFT owner)
- **token**: ERC20 token being streamed
- **snapshotTime**: Timestamp for debt calculations
- **snapshotDebtScaled**: Scaled debt amount at snapshot time
- **isTransferable**: Whether the stream NFT can be transferred
- **isVoided**: Whether the stream has been permanently voided

#### Key Mappings
- `_streams`: Maps stream IDs to Stream structs
- `aggregateBalance`: Tracks total token balances per ERC20 token
- `protocolFee`: Configurable fees per token (UD60x18 format)
- `protocolRevenue`: Accumulated protocol revenue per token
- `isTokenAssociated`: Hedera token association status

## Getting Started

### Prerequisites

- [Foundry](https://book.getfoundry.sh/getting-started/installation.html)
- [Node.js](https://nodejs.org/) (for TypeScript scripts)
- [Git](https://git-scm.com/)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/NaDasai/Streamera.git
cd Streamera/contracts
```

2. Install dependencies:

```bash
forge install
npm install
```

3. Build the contracts:

```bash
forge build
```

4. Run tests:

```bash
forge test
```

### Deployment

#### Local Development

```bash
# Start local Anvil node
anvil

# Deploy Streamera contract to local network
forge script script/DeployStreamera.s.sol --broadcast --fork-url http://localhost:8545
```

#### Hedera Testnet

```bash
# Deploy Streamera to Hedera testnet with verification
npm run deploy
```

#### Hedera Mainnet

```bash
# Deploy Streamera to Hedera mainnet
forge script script/DeployStreamera.s.sol --rpc-url hedera_mainnet --broadcast --verify --verifier sourcify --verifier-url https://server-verify.hashscan.io
```

## Usage

### Creating a Stream

```solidity
// Example: Create a stream paying 1 token per second
uint256 streamId = streamera.create(
    senderAddress,     // Address that will fund the stream
    recipientAddress,  // Stream recipient (receives NFT)
    UD21x18.wrap(1e18), // 1 token per second (UD21x18 format)
    IERC20(tokenAddress), // ERC20 token to stream
    true               // Whether NFT is transferable
);
```

### Creating and Funding a Stream

```solidity
// Create and immediately fund a stream
uint256 streamId = streamera.createAndDeposit(
    senderAddress,
    recipientAddress,
    UD21x18.wrap(1e18), // Rate per second
    IERC20(tokenAddress),
    true,              // Transferable
    1000e18           // Initial deposit amount
);
```

### Depositing to an Existing Stream

```solidity
// Add funds to an existing stream
streamera.deposit(
    streamId,         // Stream ID
    500e18,          // Amount to deposit
    senderAddress,   // Original sender
    recipientAddress // Original recipient
);
```

### Withdrawing from Stream

```solidity
// Withdraw specific amount
(uint128 withdrawn, uint128 protocolFee) = streamera.withdraw(
    streamId,        // Stream ID
    recipientAddress, // Where to send tokens
    100e18          // Amount to withdraw
);

// Withdraw maximum available amount
(uint128 withdrawn, uint128 protocolFee) = streamera.withdrawMax(
    streamId,
    recipientAddress
);
```

### Stream Management Operations

```solidity
// Pause a stream (sender only)
streamera.pause(streamId);

// Refund tokens back to sender
streamera.refund(streamId, 200e18);

// Refund all available tokens
streamera.refundMax(streamId);

// Permanently void a stream
streamera.void(streamId);
```

## Contract Functions

### Core Stream Functions

- `create()` - Create a new payment stream
- `createAndDeposit()` - Create and immediately fund a stream
- `deposit()` - Add tokens to an existing stream
- `withdraw()` - Withdraw specific amount from stream
- `withdrawMax()` - Withdraw maximum available amount

### Stream Management

- `pause()` - Pause a stream (sender only)
- `refund()` - Refund specific amount to sender
- `refundMax()` - Refund all available tokens to sender
- `void()` - Permanently void a stream (sender or recipient)

### Administrative Functions

- `setProtocolFee()` - Set protocol fee for a token (owner only)
- `collectProtocolRevenue()` - Collect accumulated protocol fees
- `collectNativeFees()` - Collect native HBAR fees

### View Functions

- `getStream()` - Get complete stream information
- `statusOf()` - Get current stream status
- `totalDebtOf()` - Get total debt amount
- `coveredDebtOf()` - Get covered debt amount
- `uncoveredDebtOf()` - Get uncovered debt amount
- `refundableAmountOf()` - Get refundable amount for sender

## Stream States and Status

Streamera uses an enum-based status system to track stream states:

- **STREAMING_SOLVENT**: Stream is active with sufficient balance to cover debt
- **STREAMING_INSOLVENT**: Stream is active but balance cannot cover total debt
- **PAUSED_SOLVENT**: Stream is paused with sufficient balance
- **PAUSED_INSOLVENT**: Stream is paused with insufficient balance
- **VOIDED**: Stream is permanently ended and cannot be restarted

## Mathematical Precision

Streamera uses PRB Math library for precise calculations:

- **UD21x18**: 21-digit precision for rate per second calculations
- **UD60x18**: 60-digit precision for protocol fee percentages
- **Real-time Debt Calculation**: `debt = ratePerSecond * timeElapsed`
- **Scaled Arithmetic**: Handles different token decimals automatically

## Security Features

- **Reentrancy Protection**: Uses OpenZeppelin's ReentrancyGuard for all state-changing functions
- **Delegate Call Prevention**: NoDelegateCall modifier prevents proxy-related attacks
- **Safe Token Transfers**: Implements SafeERC20 and custom TransferHelper for secure transfers
- **Access Control**: Comprehensive permission system with sender/recipient/owner checks
- **Input Validation**: Extensive parameter validation and boundary checks
- **Overflow Protection**: SafeCast library prevents arithmetic overflows
- **Protocol Fee Limits**: Maximum fee cap (10%) prevents excessive fees

## Testing

Run the full test suite:

```bash
forge test
```

Run tests with gas reporting:

```bash
forge test --gas-report
```

Run tests on Hedera testnet fork:

```bash
npm run test:fork
```

Run specific test file:

```bash
forge test --match-path test/Streamera.t.sol
```

## Development

### Code Formatting

```bash
forge fmt
```

### Gas Snapshots

```bash
forge snapshot
```

### Coverage Report

```bash
forge coverage
```

### TypeScript Scripts

Create a stream on Hedera testnet:

```bash
npm run create-stream
```

Deposit tokens to a stream:

```bash
npm run deposit-token
```

## Project Structure

```
Streamera/
├── README.md                    # Project overview and documentation
├── contracts/                   # Foundry project directory
│   ├── README.md               # Contracts-specific documentation
│   ├── foundry.toml            # Foundry configuration
│   ├── foundry.lock            # Dependency lock file
│   ├── remappings.txt          # Import remappings
│   ├── package.json            # NPM package configuration
│   ├── cmds.md                 # Command documentation
│   ├── src/                    # Source contracts
│   │   ├── Streamera.sol       # Main Streamera contract
│   │   ├── base/               # Base contracts
│   │   │   ├── StreameraBase.sol # Base functionality
│   │   │   ├── Batch.sol       # Batch operations
│   │   │   └── NoDelegateCall.sol # Delegate call prevention
│   │   ├── interfaces/         # Contract interfaces
│   │   │   ├── IStreamera.sol  # Main interface
│   │   │   ├── IBatch.sol      # Batch interface
│   │   │   └── IHederaTokenService.sol # Hedera integration
│   │   ├── libraries/          # Utility libraries
│   │   │   ├── TransferHelper.sol # Safe token transfers
│   │   │   ├── AssociateHelper.sol # Token association
│   │   │   ├── Helpers.sol     # General utilities
│   │   │   └── Errors.sol      # Custom errors
│   │   └── types/              # Data structures
│   │       └── DataTypes.sol   # Stream and related types
│   ├── test/                   # Test files
│   │   ├── Streamera.t.sol     # Main contract tests
│   │   └── mocks/              # Mock contracts
│   │       ├── MockERC20.sol   # Mock ERC20 token
│   │       └── MockHederaTokenService.sol
│   ├── script/                 # Deployment and utility scripts
│   │   ├── DeployStreamera.s.sol # Main deployment script
│   │   └── hedera_testnet/     # Hedera-specific scripts
│   │       ├── create-stream.ts # Stream creation script
│   │       └── deposit-token.ts # Token deposit script
│   ├── lib/                    # External libraries
│   │   ├── forge-std/          # Foundry standard library
│   │   ├── openzeppelin-contracts/ # OpenZeppelin contracts
│   │   └── prb-math/           # PRB Math library
│   ├── broadcast/              # Deployment broadcast logs
│   ├── cache/                  # Foundry cache
│   ├── out/                    # Compiled contracts
│   └── dist/                   # TypeScript compilation output
```

## Dependencies

- **OpenZeppelin Contracts**: ^5.0.0 - Industry-standard smart contract libraries
- **PRB Math**: Latest - Fixed-point arithmetic library for precise calculations
- **Forge Standard Library**: Latest - Testing and development utilities
- **TypeScript**: ^5.9.2 - For deployment and interaction scripts
- **Ethers.js**: ^6.15.0 - Ethereum library for JavaScript/TypeScript

## Network Support

- **Hedera Testnet**: For development and testing (`hedera_testnet`)
- **Hedera Mainnet**: For production deployment (`hedera_mainnet`)
- **Local Anvil**: For local development and testing

## Key Features Comparison

| Feature | Previous (Hourglass) | Current (Streamera) |
|---------|---------------------|---------------------|
| Architecture | Factory + Payer contracts | Single unified contract |
| Stream Creation | Time-based (start/end) | Rate-based (per second) |
| Mathematics | Basic arithmetic | PRB Math fixed-point |
| NFT Integration | Basic ERC721 | Full ERC721 with transferability control |
| Fee System | Simple fees | Protocol fees with revenue tracking |
| Batch Operations | Not supported | Full batch operation support |
| Security | Basic protections | Comprehensive security suite |
| Token Association | Manual | Automatic Hedera integration |

## Gas Optimization

Streamera includes several gas optimization features:

- **Packed Structs**: Efficient storage layout in `DataTypes.Stream`
- **Batch Operations**: Execute multiple operations in single transaction
- **Unchecked Arithmetic**: Safe unchecked blocks where overflow is impossible
- **Efficient Mappings**: Optimized storage access patterns
- **Minimal External Calls**: Reduced external contract interactions

## Events and Monitoring

Streamera emits comprehensive events for monitoring:

- `StreamCreated` - New stream creation
- `DepositToStream` - Token deposits
- `WithdrawFromStream` - Token withdrawals
- `PauseStream` - Stream pausing
- `RestartStream` - Stream resumption
- `RefundFromStream` - Token refunds
- `VoidStream` - Stream voiding
- `AdjustStreamRatePerSecond` - Rate adjustments
- `SetProtocolFee` - Fee updates
- `CollectProtocolRevenue` - Revenue collection
- `CollectNativeFees` - Native fee collection

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Solidity style guide and best practices
- Add comprehensive tests for new features
- Update documentation for any API changes
- Ensure gas efficiency in new implementations
- Use PRB Math for all fixed-point arithmetic

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions and support:

- Open an issue on GitHub
- Check the [Foundry documentation](https://book.getfoundry.sh/)
- Review [Hedera documentation](https://docs.hedera.com/)
- Explore [PRB Math documentation](https://github.com/PaulRBerg/prb-math)

## Acknowledgments

- Built with [Foundry](https://book.getfoundry.sh/) for development and testing
- Deployed on [Hedera](https://hedera.com/) for enterprise-grade performance
- Uses [PRB Math](https://github.com/PaulRBerg/prb-math) for precise calculations
- Secured with [OpenZeppelin](https://openzeppelin.com/) contracts

---

**Streamera** - Next-generation streaming payments on Hedera 🚀</content>
