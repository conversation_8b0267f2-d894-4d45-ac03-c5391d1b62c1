**Calculate flow rate by seconds(how much amount each second)**

rate = ( total_amount \* 10 \*\* 20 ) / total_secs_of_stream

Stremera contract : 0x36BcE29839Db8DC5cbA2dC64200A729558baB8FD


**Signature of custom error**
```bash
cast keccak "NOT_OWNER_OR_WHITELISTED()" | cut -c1-10
cast keccak "INVALID_ADDRESS()" | cut -c1-10
cast keccak "INVALID_TIME()" | cut -c1-10
cast keccak "PAYER_IN_DEBT()" | cut -c1-10
cast keccak "INACTIVE_STREAM()" | cut -c1-10
cast keccak "ACTIVE_STREAM()" | cut -c1-10
cast keccak "STREAM_ACTIVE_OR_REDEEMABLE()" | cut -c1-10
cast keccak "STREAM_ENDED()" | cut -c1-10
cast keccak "STREAM_DOES_NOT_EXIST()" | cut -c1-10
cast keccak "TOKEN_NOT_ADDED()" | cut -c1-10
cast keccak "INVALID_AMOUNT()" | cut -c1-10
cast keccak "ALREADY_WHITELISTED()" | cut -c1-10
cast keccak "NOT_WHITELISTED()" | cut -c1-10

0xba1b8c53
0x5963709b
0xa6b9f220
0xef98a284
0xf7136313
0xf9409145
0xfee13fc3
0x1e47cb82
0xf87acb5d
0x6fed98bf
0xfae82791
0xa0027c1e
0xbffbc6be
```