// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {ReentrancyGuard} from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ERC721} from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {SafeCast} from "@openzeppelin/contracts/utils/math/SafeCast.sol";
import {DataTypes} from "../types/DataTypes.sol";
import {Errors} from "../libraries/Errors.sol";
import {NoDelegateCall} from "./NoDelegateCall.sol";
import {Batch} from "./Batch.sol";
import {IStreamera} from "../interfaces/IStreamera.sol";
import {TransferHelper} from "../libraries/TransferHelper.sol";
import {AssociateHelper} from "../libraries/AssociateHelper.sol";
import {UD60x18, ZERO} from "@prb/math/src/UD60x18.sol";

abstract contract StreameraBase is
    Ownable,
    ERC721,
    NoDelegateCall,
    Batch,
    ReentrancyGuard
{
    using SafeERC20 for ERC20;
    using SafeCast for uint256;

    /// @notice Retrieves the maximum fee that can be charged by the broker and the protocol, denoted as a fixed-point percentage where 1e18 is 100%.
    UD60x18 public constant MAX_FEE = UD60x18.wrap(0.1e18);

    /// @notice Counter for the next stream ID.
    uint256 public nextStreamId;

    /// @notice Retrieves the stream by its ID.
    mapping(uint256 id => DataTypes.Stream stream) internal _streams;

    /// @notice Retrieves the sum of balances of all streams.
    mapping(IERC20 token => uint256 amount) public aggregateBalance;

    /// @notice Protocol fee for the provided ERC-20 token, denoted as a fixed-point percentage where 1e18 is 100%.
    mapping(IERC20 token => UD60x18 fee) public protocolFee;

    /// @notice Protocol revenue for the provided ERC-20 token, denoted in the token's decimals.
    mapping(IERC20 token => uint128 revenue) public protocolRevenue;

    /// @notice Retrieves whether a token is associated with this contract.
    mapping(IERC20 token => bool isAssociated) public isTokenAssociated;

    constructor() ERC721("Streamera", "STREM") Ownable(msg.sender) {}

    /// @notice associate a token to this contract (Hedera Special)
    /// @param _token token address
    function associateToken(address _token) external onlyOwner {
        AssociateHelper.safeAssociateToken(address(this), _token);
        isTokenAssociated[IERC20(_token)] = true;
    }

    /// @notice associate multiple tokens to this contract (Hedera Special)
    /// @param _tokens array of token addresses
    function associateTokens(address[] calldata _tokens) external onlyOwner {
        for (uint i = 0; i < _tokens.length; i++) {
            AssociateHelper.safeAssociateToken(address(this), _tokens[i]);
            isTokenAssociated[IERC20(_tokens[i])] = true;
        }
    }

    /**
     * /////////////////////////////////////////////////////////
     *                     MOFIFIERS
     * /////////////////////////////////////////////////////////
     */

    /// @dev Checks that `streamId` does not reference a null stream.
    modifier notNull(uint256 streamId) {
        if (!_streams[streamId].isStream) {
            revert Errors.Null(streamId);
        }
        _;
    }

    /// @dev Checks that `streamId` does not reference a paused stream.
    modifier notPaused(uint256 streamId) {
        if (_streams[streamId].ratePerSecond.unwrap() == 0) {
            revert Errors.StreamPaused(streamId);
        }
        _;
    }

    /// @dev Checks that `streamId` does not reference a voided stream.
    modifier notVoided(uint256 streamId) {
        if (_streams[streamId].isVoided) {
            revert Errors.StreamVoided(streamId);
        }
        _;
    }

    /// @dev Checks the `msg.sender` is the stream's sender.
    modifier onlyStreamSender(uint256 streamId) {
        if (msg.sender != _streams[streamId].sender) {
            revert Errors.Unauthorized(streamId, msg.sender);
        }
        _;
    }
}
