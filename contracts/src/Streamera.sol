// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {ReentrancyGuard} from "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ERC721} from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {SafeCast} from "@openzeppelin/contracts/utils/math/SafeCast.sol";
import {DataTypes} from "./types/DataTypes.sol";
import {Errors} from "./libraries/Errors.sol";
import {NoDelegateCall} from "./base/NoDelegateCall.sol";
import {Batch} from "./base/Batch.sol";
import {IStreamera} from "./interfaces/IStreamera.sol";
import {TransferHelper} from "./libraries/TransferHelper.sol";
import {AssociateHelper} from "./libraries/AssociateHelper.sol";
import {Helpers} from "./libraries/Helpers.sol";
import {UD60x18, ZERO} from "@prb/math/src/UD60x18.sol";
import {UD21x18, ud21x18} from "@prb/math/src/UD21x18.sol";
import {StreameraBase} from "./base/StreameraBase.sol";

/// @title Streamera
/// @dev This contract is designed to handle money streaming payments
contract Streamera is IStreamera, StreameraBase {
    using SafeERC20 for ERC20;
    using SafeCast for uint256;

    /**
     * /////////////////////////////////////////////////////////
     *                  WRITE FUNCTIONS
     * /////////////////////////////////////////////////////////
     */
    function create(
        address sender,
        address recipient,
        UD21x18 ratePerSecond,
        IERC20 token,
        bool transferable
    ) external payable noDelegateCall returns (uint256 streamId) {
        streamId = _createStream(
            sender,
            recipient,
            ratePerSecond,
            token,
            transferable
        );
    }

    function deposit(
        uint256 streamId,
        uint128 amount,
        address sender,
        address recipient
    ) external payable noDelegateCall notNull(streamId) notVoided(streamId) {
        // Check: the provided sender and recipient match the stream's sender and recipient.
        _verifyStreamSenderRecipient(streamId, sender, recipient);

        _deposit(streamId, amount);
    }

    function createAndDeposit(
        address sender,
        address recipient,
        UD21x18 ratePerSecond,
        IERC20 token,
        bool transferable,
        uint128 amount
    ) external payable noDelegateCall returns (uint256 streamId) {
        streamId = _createStream(
            sender,
            recipient,
            ratePerSecond,
            token,
            transferable
        );

        _deposit(streamId, amount);
    }

    /// @dev withdraw from stream
    /// @param streamId id of stream
    /// @param to address to withdraw to
    /// @param amount amount to withdraw
    /// @return withdrawnAmount The amount withdrawn to the recipient, denoted in token's decimals. This is input amount minus the protocol fee.
    /// @return protocolFeeAmount The protocol fee amount, denoted in the token's decimals.
    function withdraw(
        uint256 streamId,
        address to,
        uint128 amount
    )
        external
        payable
        noDelegateCall
        notNull(streamId)
        returns (uint128 withdrawnAmount, uint128 protocolFeeAmount)
    {
        (withdrawnAmount, protocolFeeAmount) = _withdraw(streamId, to, amount);
    }

    /// @dev Withdraws the maximum amount of tokens from the stream.
    /// @param streamId The ID of the stream.
    /// @param to The address to withdraw to.
    /// @return withdrawnAmount The amount withdrawn to the recipient, denoted in token's decimals. This is input amount minus the protocol fee.
    /// @return protocolFeeAmount The protocol fee amount, denoted in the token's decimals.
    function withdrawMax(
        uint256 streamId,
        address to
    )
        external
        payable
        noDelegateCall
        notNull(streamId)
        returns (uint128 withdrawnAmount, uint128 protocolFeeAmount)
    {
        uint128 coveredDebt = _coveredDebtOf(streamId);

        // Checks, Effects, and Interactions: make the withdrawal.
        (withdrawnAmount, protocolFeeAmount) = _withdraw(
            streamId,
            to,
            coveredDebt
        );
    }

    /// @dev Pauses the stream.
    /// @param streamId The id of the stream to pause.
    function pause(
        uint256 streamId
    )
        external
        payable
        noDelegateCall
        notNull(streamId)
        notPaused(streamId)
        onlyStreamSender(streamId)
    {
        _pause(streamId);
    }

    /// @dev Refunds the provided amount from the stream to the sender.
    /// @param streamId The id of the stream to refund from.
    /// @param amount The amount to refund, denoted in token's decimals.
    function refund(
        uint256 streamId,
        uint128 amount
    )
        external
        payable
        noDelegateCall
        notNull(streamId)
        onlyStreamSender(streamId)
    {
        _refund(streamId, amount);
    }

    /// @dev Refunds all the available amount from the stream to the sender.
    /// @param streamId The id of the stream to refund from.
    function refundMax(
        uint256 streamId
    )
        external
        payable
        noDelegateCall
        notNull(streamId)
        onlyStreamSender(streamId)
    {
        uint128 refundableAmount = _refundableAmountOf(streamId);

        _refund(streamId, refundableAmount);
    }

    /// @dev Voids the provided stream by setting the rate per second to zero and the snapshot debt to the stream balance.
    /// @param streamId  The id of the stream to void.
    function void(
        uint256 streamId
    ) external payable noDelegateCall notNull(streamId) notVoided(streamId) {
        // Checks, Effects, and Interactions: void the stream.
        _void(streamId);
    }

    /// @dev Adjusts the rate per second of the stream.
    /// @param streamId The ID of the stream.
    /// @param newRatePerSecond The new rate per second, denoted as a fixed-point number where 1e18 is 1 token per second.
    function adjustRatePerSecond(
        uint256 streamId,
        UD21x18 newRatePerSecond
    )
        external
        noDelegateCall
        notNull(streamId)
        notPaused(streamId)
        onlyStreamSender(streamId)
    {
        UD21x18 oldRatePerSecond = _streams[streamId].ratePerSecond;

        // Effects and Interactions: adjust the rate per second.
        _adjustRatePerSecond(streamId, newRatePerSecond);

        // Log the adjustment.
        emit IStreamera.AdjustStreamRatePerSecond({
            streamId: streamId,
            totalDebt: _totalDebtOf(streamId),
            oldRatePerSecond: oldRatePerSecond,
            newRatePerSecond: newRatePerSecond
        });
    }

    /**
     * /////////////////////////////////////////////////////////
     * **************** VIEW FUNCTIONS ***************
     * /////////////////////////////////////////////////////////
     */

    function getStream(
        uint256 streamId
    ) external view notNull(streamId) returns (DataTypes.Stream memory stream) {
        stream = _streams[streamId];
    }

    function getStreamBalance(
        uint256 streamId
    ) external view notNull(streamId) returns (uint256) {
        return _streams[streamId].balance;
    }

    function getStreamRatePerSecond(
        uint256 streamId
    ) external view notNull(streamId) returns (UD21x18) {
        return _streams[streamId].ratePerSecond;
    }

    function getStreamSender(
        uint256 streamId
    ) external view notNull(streamId) returns (address) {
        return _streams[streamId].sender;
    }

    /// @dev Returns the stream's recipient address.
    /// @notice The Receipient is teh owner of the stream NFT.
    /// @param streamId The ID of the stream.
    function getStreamRecipient(
        uint256 streamId
    ) external view notNull(streamId) returns (address recipient) {
        recipient = _ownerOf(streamId);
    }

    function getStreamToken(
        uint256 streamId
    ) external view notNull(streamId) returns (IERC20) {
        return _streams[streamId].token;
    }

    function getStreamTokenDecimals(
        uint256 streamId
    ) external view notNull(streamId) returns (uint8) {
        return _streams[streamId].tokenDecimals;
    }

    function withdrawableAmountOf(
        uint256 streamId
    ) external view notNull(streamId) returns (uint128 withdrawableAmount) {
        withdrawableAmount = _coveredDebtOf(streamId);
    }

    function refundableAmountOf(
        uint256 streamId
    ) external view notNull(streamId) returns (uint128 refundableAmount) {
        refundableAmount = _refundableAmountOf(streamId);
    }

    /// @dev Returns the total debt of the stream.
    function totalDebtOf(
        uint256 streamId
    ) external view notNull(streamId) returns (uint256 totalDebt) {
        totalDebt = _totalDebtOf(streamId);
    }

    /// @dev Returns the amount of covered debt by the id.
    /// @notice The covered debt is the amount of debt that has been covered by the stream balance.
    function coveredDebtOf(
        uint256 streamId
    ) external view notNull(streamId) returns (uint128 coveredDebt) {
        coveredDebt = _coveredDebtOf(streamId);
    }

    /// @dev Returns the amount of uncovered debt by the id.
    /// @notice The uncovered debt is the amount of debt that has not been covered by the stream balance.
    function uncoveredDebtOf(
        uint256 streamId
    ) external view notNull(streamId) returns (uint256 uncoveredDebt) {
        uncoveredDebt = _uncoveredDebtOf(streamId);
    }

    /**
     * /////////////////////////////////////////////////////////
     *                  Internals FUNCTIONS
     * /////////////////////////////////////////////////////////
     */

    /// @dev See the documentation for the user-facing functions that call this internal function.
    function _createStream(
        address sender,
        address recipient,
        UD21x18 ratePerSecond,
        IERC20 token,
        bool transferable
    ) internal returns (uint256 streamId) {
        // Check: the sender is not the zero address.
        if (sender == address(0)) {
            revert Errors.SenderZeroAddress();
        }

        // If the token is not associated with this contract, associate it.
        if (!isTokenAssociated[token]) {
            AssociateHelper.safeAssociateToken(address(this), address(token));
            isTokenAssociated[token] = true;
        }

        uint8 tokenDecimals = IERC20Metadata(address(token)).decimals();

        // Check: the token decimals are not greater than 18.
        if (tokenDecimals > 18) {
            revert Errors.InvalidTokenDecimals(address(token));
        }

        // Load the stream ID.
        streamId = nextStreamId;

        // Effect: create the stream.
        _streams[streamId] = DataTypes.Stream({
            balance: 0,
            isStream: true,
            isTransferable: transferable,
            isVoided: false,
            ratePerSecond: ratePerSecond,
            sender: sender,
            snapshotDebtScaled: 0,
            snapshotTime: uint40(block.timestamp),
            token: token,
            tokenDecimals: tokenDecimals
        });

        // Using unchecked arithmetic because this calculation can never realistically overflow.
        unchecked {
            nextStreamId = streamId + 1;
        }

        // Mint the stream NFT to the recipient.
        _safeMint(recipient, streamId);

        // Log the newly created stream.
        emit IStreamera.StreamCreated({
            streamId: streamId,
            sender: sender,
            recipient: recipient,
            ratePerSecond: ratePerSecond,
            token: token,
            transferable: transferable
        });
    }

    /// @dev Checks whether the provided addresses matches stream's sender and recipient.
    function _verifyStreamSenderRecipient(
        uint256 streamId,
        address sender,
        address recipient
    ) internal view {
        if (sender != _streams[streamId].sender) {
            revert Errors.NotStreamSender(sender, _streams[streamId].sender);
        }

        if (recipient != _ownerOf(streamId)) {
            revert Errors.NotStreamRecipient(recipient, _ownerOf(streamId));
        }
    }

    /// @dev
    function _deposit(uint256 streamId, uint128 amount) internal {
        // Check: the deposit amount is not zero.
        if (amount == 0) {
            revert Errors.DepositAmountZero(streamId);
        }

        IERC20 token = _streams[streamId].token;

        // Update the stream balance.
        _streams[streamId].balance += amount;

        unchecked {
            // Update the aggregate balance.
            aggregateBalance[token] += amount;
        }

        // Transfer the amount from the sender to the HashPay contract.
        TransferHelper.safeTransferFrom(
            address(token),
            msg.sender,
            address(this),
            amount
        );

        // Log the deposit.
        emit IStreamera.DepositToStream({
            streamId: streamId,
            funder: msg.sender,
            amount: amount
        });
    }

    /// @dev Withdraw from stream
    /// @param streamId id of stream
    /// @param to address to withdraw to
    /// @param amount amount to withdraw
    /// @return withdrawnAmount The amount withdrawn to the recipient, denoted in token's decimals. This is input amount minus the protocol fee.
    /// @return protocolFeeAmount The protocol fee amount, denoted in the token's decimals.
    function _withdraw(
        uint256 streamId,
        address to,
        uint128 amount
    ) internal returns (uint128 withdrawnAmount, uint128 protocolFeeAmount) {
        // Check: the withdraw amount is not zero.
        if (amount == 0) {
            revert Errors.WithdrawAmountZero(streamId);
        }

        // Check: the withdrawal address is not zero.
        if (to == address(0)) {
            revert Errors.WithdrawToZeroAddress(streamId);
        }

        // Check: `msg.sender` is neither the stream's recipient nor an approved third party, the withdrawal address
        // must be the recipient.
        if (
            to != _ownerOf(streamId) &&
            !_isCallerStreamRecipientOrApproved(streamId)
        ) {
            revert Errors.WithdrawalAddressNotRecipient({
                streamId: streamId,
                caller: msg.sender,
                to: to
            });
        }

        uint8 tokenDecimals = _streams[streamId].tokenDecimals;

        // Calculate the total debt.
        uint256 totalDebtScaled = _ongoingDebtScaledOf(streamId) +
            _streams[streamId].snapshotDebtScaled;

        uint256 totalDebt = Helpers.descaleAmount(
            totalDebtScaled,
            tokenDecimals
        );

        // Calculate the withdrawable amount.
        uint128 balance = _streams[streamId].balance;
        uint128 withdrawableAmount;

        if (balance < totalDebt) {
            // If the stream balance is less than the total debt, the withdrawable amount is the balance.
            withdrawableAmount = balance;
        } else {
            // Otherwise, the withdrawable amount is the total debt.
            withdrawableAmount = totalDebt.toUint128();
        }

        // Check: the withdraw amount is not greater than the withdrawable amount.
        if (amount > withdrawableAmount) {
            revert Errors.Overdraw(streamId, amount, withdrawableAmount);
        }

        // Calculate the amount scaled.
        uint256 amountScaled = Helpers.scaleAmount(amount, tokenDecimals);

        // Safe to use unchecked, `amount` cannot be greater than the balance or total debt at this point.
        unchecked {
            // If the amount is less than the snapshot debt, reduce it from the snapshot debt and leave the snapshot
            // time unchanged.
            if (amountScaled <= _streams[streamId].snapshotDebtScaled) {
                _streams[streamId].snapshotDebtScaled -= amountScaled;
            }
            // Else reduce the amount from the ongoing debt by setting snapshot time to `block.timestamp` and set the
            // snapshot debt to the remaining total debt.
            else {
                _streams[streamId].snapshotDebtScaled =
                    totalDebtScaled -
                    amountScaled;

                // Effect: update the stream time.
                _streams[streamId].snapshotTime = uint40(block.timestamp);
            }

            // Update the stream balance.
            _streams[streamId].balance -= amount;
        }

        // Load the variables in memory.
        IERC20 token = _streams[streamId].token;

        UD60x18 _protocolFee = protocolFee[token];

        if (_protocolFee > ZERO) {
            // Calculate the protocol fee amount and the net withdraw amount.
            (protocolFeeAmount, amount) = Helpers.calculateAmountsFromFee({
                totalAmount: amount,
                fee: _protocolFee
            });

            // Safe to use unchecked because addition cannot overflow.
            unchecked {
                // Update the protocol revenue.
                protocolRevenue[token] += protocolFeeAmount;
            }
        }

        unchecked {
            // Update the aggregate balance.
            aggregateBalance[token] -= amount;
        }

        // Transfer the amount from the sender to the HashPay contract.
        TransferHelper.safeTransfer(address(token), to, amount);

        // Protocol Invariant: the difference in total debt should be equal to the difference in the stream balance.
        assert(
            totalDebt - _totalDebtOf(streamId) ==
                balance - _streams[streamId].balance
        );

        // Log the withdrawal.
        emit IStreamera.WithdrawFromStream({
            streamId: streamId,
            to: to,
            token: token,
            caller: msg.sender,
            withdrawAmount: amount,
            protocolFeeAmount: protocolFeeAmount
        });

        return (amount, protocolFeeAmount);
    }

    /// @dev Adjusts the rate per second of the stream.
    function _adjustRatePerSecond(
        uint256 streamId,
        UD21x18 newRatePerSecond
    ) internal {
        // Check: the new rate per second is different from the current rate per second.
        if (
            newRatePerSecond.unwrap() ==
            _streams[streamId].ratePerSecond.unwrap()
        ) {
            revert Errors.RatePerSecondNotDifferent(streamId, newRatePerSecond);
        }

        uint256 ongoingDebtScaled = _ongoingDebtScaledOf(streamId);

        // Update the snapshot debt only if the stream has ongoing debt.
        if (ongoingDebtScaled > 0) {
            // Update the snapshot debt.
            _streams[streamId].snapshotDebtScaled += ongoingDebtScaled;
        }

        // Update the snapshot time.
        _streams[streamId].snapshotTime = uint40(block.timestamp);

        // Set the new rate per second.
        _streams[streamId].ratePerSecond = newRatePerSecond;
    }

    /// @dev Pauses the stream.
    function _pause(uint256 streamId) internal {
        _adjustRatePerSecond({
            streamId: streamId,
            newRatePerSecond: ud21x18(0)
        });

        // Log the pause.
        emit IStreamera.PauseStream({
            streamId: streamId,
            sender: _streams[streamId].sender,
            recipient: _ownerOf(streamId),
            totalDebt: _totalDebtOf(streamId)
        });
    }

    /// @dev Refunds the provided amount from the stream to the sender.
    function _refund(uint256 streamId, uint128 amount) internal {
        // Check: the refund amount is not zero.
        if (amount == 0) {
            revert Errors.RefundAmountZero(streamId);
        }

        // Calculate the refundable amount.
        uint128 refundableAmount = _refundableAmountOf(streamId);

        // Check: the refund amount is not greater than the refundable amount.
        if (amount > refundableAmount) {
            revert Errors.RefundOverflow(streamId, amount, refundableAmount);
        }

        // Although the refundable amount should never exceed the balance, this condition is checked
        // to avoid exploits in case of a bug.
        if (refundableAmount > _streams[streamId].balance) {
            revert Errors.InvalidCalculation(
                streamId,
                _streams[streamId].balance,
                amount
            );
        }

        address sender = _streams[streamId].sender;
        IERC20 token = _streams[streamId].token;

        // Safe to use unchecked because at this point, the amount cannot exceed the balance.
        unchecked {
            // Effect: update the stream balance.
            _streams[streamId].balance -= amount;

            // Effect: update the aggregate balance.
            aggregateBalance[token] -= amount;
        }

        // Perform the ERC-20(HTS) transfer.
        TransferHelper.safeTransfer(address(token), sender, amount);

        // Log the refund.
        emit IStreamera.RefundFromStream(streamId, sender, amount);
    }

    /// @dev Voids the provided stream by setting the rate per second to zero and the snapshot debt to the stream balance.
    function _void(uint256 streamId) internal {
        // Check: `msg.sender` is either the stream's sender, recipient or an approved third party.
        if (
            msg.sender != _streams[streamId].sender &&
            !_isCallerStreamRecipientOrApproved(streamId)
        ) {
            revert Errors.Unauthorized({
                streamId: streamId,
                caller: msg.sender
            });
        }

        uint256 debtToWriteOff = _uncoveredDebtOf(streamId);

        // If the stream is solvent, update the total debt normally.
        if (debtToWriteOff == 0) {
            uint256 ongoingDebtScaled = _ongoingDebtScaledOf(streamId);
            if (ongoingDebtScaled > 0) {
                // Effect: Update the snapshot debt by adding the ongoing debt.
                _streams[streamId].snapshotDebtScaled += ongoingDebtScaled;
            }
        }
        // If the stream is insolvent, write off the uncovered debt.
        else {
            // Effect: update the total debt by setting snapshot debt to the stream balance.
            _streams[streamId].snapshotDebtScaled = Helpers.scaleAmount({
                amount: _streams[streamId].balance,
                decimals: _streams[streamId].tokenDecimals
            });
        }

        // Effect: update the snapshot time.
        _streams[streamId].snapshotTime = uint40(block.timestamp);

        // Effect: set the rate per second to zero.
        _streams[streamId].ratePerSecond = ud21x18(0);

        // Effect: set the stream as voided.
        _streams[streamId].isVoided = true;

        // Log the void.
        emit IStreamera.VoidStream({
            streamId: streamId,
            sender: _streams[streamId].sender,
            recipient: _ownerOf(streamId),
            caller: msg.sender,
            newTotalDebt: _totalDebtOf(streamId),
            writtenOffDebt: debtToWriteOff
        });
    }

    /// @notice Checks whether `msg.sender` is the stream's recipient or an approved third party.
    /// @param streamId The stream ID for the query.
    function _isCallerStreamRecipientOrApproved(
        uint256 streamId
    ) internal view returns (bool) {
        address recipient = _ownerOf(streamId);
        return
            msg.sender == recipient ||
            isApprovedForAll({owner: recipient, operator: msg.sender}) ||
            getApproved(streamId) == msg.sender;
    }

    /// @dev Calculates the refundable amount of the stream.
    function _refundableAmountOf(
        uint256 streamId
    ) internal view returns (uint128) {
        return _streams[streamId].balance - _coveredDebtOf(streamId);
    }

    /// @dev Calculates the amount of covered debt by the stream balance.
    function _coveredDebtOf(uint256 streamId) internal view returns (uint128) {
        uint128 balance = _streams[streamId].balance;

        // If the balance is zero, return zero.
        if (balance == 0) {
            return 0;
        }

        uint256 totalDebt = _totalDebtOf(streamId);

        // If the stream balance is less than or equal to the total debt, return the stream balance.
        if (balance < totalDebt) {
            return balance;
        }

        // At this point, the total debt fits within `uint128`, as it is less than or equal to the balance.
        return totalDebt.toUint128();
    }

    /// @dev Calculates the ongoing debt, as a 18-decimals fixed point number, accrued since last snapshot. Return 0 if
    /// the stream is paused or `block.timestamp` is less than or equal to snapshot time.
    function _ongoingDebtScaledOf(
        uint256 streamId
    ) internal view returns (uint256) {
        uint256 blockTimestamp = block.timestamp;
        uint256 snapshotTime = _streams[streamId].snapshotTime;

        uint256 ratePerSecond = _streams[streamId].ratePerSecond.unwrap();

        // Check:if the rate per second is zero or the `block.timestamp` is less than the `snapshotTime`.
        if (ratePerSecond == 0 || blockTimestamp <= snapshotTime) {
            return 0;
        }

        uint256 elapsedTime;

        // Safe to use unchecked because subtraction cannot underflow.
        unchecked {
            // Calculate time elapsed since the last snapshot.
            elapsedTime = blockTimestamp - snapshotTime;
        }

        // Calculate the ongoing debt scaled accrued by multiplying the elapsed time by the rate per second.
        return elapsedTime * ratePerSecond;
    }

    /// @dev The total debt is the sum of the snapshot debt and the ongoing debt descaled to token's decimal. This
    /// value is independent of the stream's balance.
    function _totalDebtOf(uint256 streamId) internal view returns (uint256) {
        uint256 totalDebtScaled = _ongoingDebtScaledOf(streamId) +
            _streams[streamId].snapshotDebtScaled;
        return
            Helpers.descaleAmount({
                amount: totalDebtScaled,
                decimals: _streams[streamId].tokenDecimals
            });
    }

    /// @dev Calculates the uncovered debt.
    function _uncoveredDebtOf(
        uint256 streamId
    ) internal view returns (uint256) {
        uint128 balance = _streams[streamId].balance;

        uint256 totalDebt = _totalDebtOf(streamId);

        if (balance < totalDebt) {
            return totalDebt - balance;
        } else {
            return 0;
        }
    }

    /// @notice Overrides the {ERC-721._update} function to check that the stream is transferable.
    ///
    /// @dev The transferable flag is ignored if the current owner is 0, as the update in this case is a mint and
    /// is allowed. Transfers to the zero address are not allowed, preventing accidental burns.
    ///
    /// @param to The address of the new recipient of the stream.
    /// @param streamId ID of the stream to update.
    /// @param auth Optional parameter. If the value is not zero, the overridden implementation will check that
    /// `auth` is either the recipient of the stream, or an approved third party.
    ///
    /// @return The original recipient of the `streamId` before the update.
    function _update(
        address to,
        uint256 streamId,
        address auth
    ) internal override returns (address) {
        address from = _ownerOf(streamId);

        if (from != address(0) && !_streams[streamId].isTransferable) {
            revert Errors.NotTransferable(streamId);
        }

        return super._update(to, streamId, auth);
    }

    /**
      //////////////////////////////////////////////////////////////////////////////////////////////
      //                                PROTOCOL OWNER FUNCTIONS                                  //
      //////////////////////////////////////////////////////////////////////////////////////////////
     */

    /// @dev Collects the protocol revenue from the protocol.
    /// @param token The contract address of the ERC-20 token to collect the revenue from.
    /// @param to The address to send the revenue to.
    /// @return revenue The amount of revenue collected, denoted in token's decimals.
    function collectProtocolRevenue(
        IERC20 token,
        address to
    ) external onlyOwner nonReentrant returns (uint128 revenue) {
        revenue = protocolRevenue[token];

        // Check: there is protocol revenue to collect.
        if (revenue == 0) {
            revert Errors.NoProtocolRevenue(address(token));
        }

        // Effect: reset the protocol revenue.
        protocolRevenue[token] = 0;

        unchecked {
            // Effect: update the aggregate balance.
            aggregateBalance[token] -= revenue;
        }

        TransferHelper.safeTransfer(address(token), to, revenue);

        emit IStreamera.CollectProtocolRevenue({
            admin: msg.sender,
            token: token,
            to: to,
            revenue: revenue
        });
    }

    function setProtocolFee(
        IERC20 token,
        UD60x18 newProtocolFee
    ) external onlyOwner {
        // Check: the new protocol fee is not greater than the maximum allowed.
        if (newProtocolFee > MAX_FEE) {
            revert Errors.ProtocolFeeTooHigh(newProtocolFee, MAX_FEE);
        }

        UD60x18 oldProtocolFee = protocolFee[token];

        //  Set the new protocol fee.
        protocolFee[token] = newProtocolFee;

        // Log the change of the protocol fee.
        emit IStreamera.SetProtocolFee({
            admin: msg.sender,
            token: token,
            oldProtocolFee: oldProtocolFee,
            newProtocolFee: newProtocolFee
        });
    }

    function collectNativeFees() external {
        uint256 feeAmount = address(this).balance;

        address owner = owner();

        // Effect: transfer the fees to the owner.
        (bool success, ) = owner.call{value: feeAmount}("");

        // Revert if the call failed.
        if (!success) {
            revert Errors.FeeTransferFail(owner, feeAmount);
        }

        // Log the fee withdrawal.
        emit IStreamera.CollectNativeFees(owner, feeAmount);
    }

    // Utils functions to  Make the contract accepts native coin transfers
    receive() external payable {}

    fallback() external payable {}
}
