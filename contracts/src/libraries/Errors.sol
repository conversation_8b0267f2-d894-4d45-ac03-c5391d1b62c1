// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {UD21x18} from "@prb/math/src/UD21x18.sol";
import {UD60x18} from "@prb/math/src/UD60x18.sol";

/// @title Errors
/// @notice Library with custom errors used across the Flow contract.
library Errors {
    /*//////////////////////////////////////////////////////////////////////////
                                      GENERICS
    //////////////////////////////////////////////////////////////////////////*/

    /// @notice Thrown when an unexpected error occurs during a batch call.
    error BatchError(bytes errorData);

    /// @notice Thrown when `msg.sender` is not the admin.
    error CallerNotAdmin(address admin, address caller);

    /// @notice Thrown when trying to delegate call to a function that disallows delegate calls.
    error DelegateCall();

    /*//////////////////////////////////////////////////////////////////////////
                                    SABLIER-FLOW
    //////////////////////////////////////////////////////////////////////////*/

    /// @notice Thrown when trying to create a stream with a broker recipient address as zero.
    error BrokerAddressZero();

    /// @notice Thrown when trying to create a stream with a broker fee more than the allowed.
    error BrokerFeeTooHigh(uint128 brokerFee, uint128 maxFee);

    /// @notice Thrown when trying to create a stream with a zero deposit amount.
    error DepositAmountZero(uint256 streamId);

    /// @notice Thrown when an unexpected error occurs during the calculation of an amount.
    error InvalidCalculation(
        uint256 streamId,
        uint128 availableAmount,
        uint128 amount
    );

    /// @notice Thrown when trying to create a stream with an token with no decimals.
    error InvalidTokenDecimals(address token);

    /// @notice Thrown when the recipient address does not match the stream's recipient.
    error NotStreamRecipient(address recipient, address streamRecipient);

    /// @notice Thrown when the sender address does not match the stream's sender.
    error NotStreamSender(address sender, address streamSender);

    /// @notice Thrown when the ID references a null stream.
    error Null(uint256 streamId);

    /// @notice Thrown when trying to withdraw an amount greater than the withdrawable amount.
    error Overdraw(
        uint256 streamId,
        uint128 amount,
        uint128 withdrawableAmount
    );

    /// @notice Thrown when trying to change the rate per second with the same rate per second.
    error RatePerSecondNotDifferent(uint256 streamId, UD21x18 ratePerSecond);

    /// @notice Thrown when trying to refund zero tokens from a stream.
    error RefundAmountZero(uint256 streamId);

    /// @notice Thrown when trying to refund an amount greater than the refundable amount.
    error RefundOverflow(
        uint256 streamId,
        uint128 refundAmount,
        uint128 refundableAmount
    );

    /// @notice Thrown when trying to create a stream with the sender as the zero address.
    error SenderZeroAddress();

    /// @notice Thrown when trying to get depletion time of a stream with zero balance.
    error StreamBalanceZero(uint256 streamId);

    /// @notice Thrown when trying to perform an action with a paused stream.
    error StreamPaused(uint256 streamId);

    /// @notice Thrown when trying to restart a stream that is not paused.
    error StreamNotPaused(uint256 streamId);

    /// @notice Thrown when trying to perform an action with a voided stream.
    error StreamVoided(uint256 streamId);

    /// @notice Thrown when `msg.sender` lacks authorization to perform an action.
    error Unauthorized(uint256 streamId, address caller);

    /// @notice Thrown when trying to withdraw to an address other than the recipient's.
    error WithdrawalAddressNotRecipient(
        uint256 streamId,
        address caller,
        address to
    );

    /// @notice Thrown when trying to withdraw zero tokens from a stream.
    error WithdrawAmountZero(uint256 streamId);

    /// @notice Thrown when trying to withdraw to the zero address.
    error WithdrawToZeroAddress(uint256 streamId);

    /*//////////////////////////////////////////////////////////////////////////
                                 SABLIER-FLOW-BASE
    //////////////////////////////////////////////////////////////////////////*/

    /// @notice Thrown when the fee transfer fails.
    error FeeTransferFail(address admin, uint256 feeAmount);

    /// @notice Thrown when trying to claim protocol revenue when the accrued amount is zero.
    error NoProtocolRevenue(address token);

    /// @notice Thrown when trying to transfer Stream NFT when transferability is disabled.
    error NotTransferable(uint256 streamId);

    /// @notice Thrown when trying to set protocol fee more than the allowed.
    error ProtocolFeeTooHigh(UD60x18 newProtocolFee, UD60x18 maxFee);

    /// @notice Thrown when trying to recover for a token with zero surplus.
    error SurplusZero(address token);

    
}
