// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {UD21x18} from "@prb/math/src/UD21x18.sol";
import {UD60x18} from "@prb/math/src/UD60x18.sol";

interface IStreamera {
    /// @notice Emitted when a new stream is created.
    /// @param streamId The ID of the newly created stream.
    /// @param sender The address streaming the tokens, which is able to adjust and pause the stream.
    /// @param recipient The address receiving the tokens, as well as the NFT owner.
    /// @param ratePerSecond The amount by which the debt is increasing every second, denoted as a fixed-point number
    /// where 1e18 is 1 token per second.
    /// @param token The contract address of the ERC-20 token to be streamed.
    /// @param transferable Boolean indicating whether the stream NFT is transferable or not.
    event StreamCreated(
        uint256 streamId,
        address indexed sender,
        address indexed recipient,
        UD21x18 ratePerSecond,
        IERC20 indexed token,
        bool transferable
    );

    /// @notice Emitted when a stream is funded.
    /// @param streamId The ID of the stream.
    /// @param funder The address that made the deposit.
    /// @param amount The amount of tokens deposited into the stream, denoted in token's decimals.
    event DepositToStream(
        uint256 indexed streamId,
        address indexed funder,
        uint128 amount
    );

    /// @notice Emitted when tokens are withdrawn from a stream by a recipient or an approved operator.
    /// @param streamId The ID of the stream.
    /// @param to The address that received the withdrawn tokens.
    /// @param token The contract address of the ERC-20 token that was withdrawn.
    /// @param caller The address that performed the withdrawal, which can be the recipient or an approved operator.
    /// @param withdrawAmount The amount withdrawn to the recipient after subtracting the protocol fee, denoted in
    /// token's decimals.
    /// @param protocolFeeAmount The amount of protocol fee deducted from the withdrawn amount, denoted in token's
    /// decimals.
    event WithdrawFromStream(
        uint256 indexed streamId,
        address indexed to,
        IERC20 indexed token,
        address caller,
        uint128 withdrawAmount,
        uint128 protocolFeeAmount
    );

    /// @notice Emitted when the rate per second is updated by the sender.
    /// @param streamId The ID of the stream.
    /// @param totalDebt The total debt at the time of the update, denoted in token's decimals.
    /// @param oldRatePerSecond The old rate per second, denoted as a fixed-point number where 1e18 is 1 token
    /// per second.
    /// @param newRatePerSecond The new rate per second, denoted as a fixed-point number where 1e18 is 1 token
    /// per second.
    event AdjustStreamRatePerSecond(
        uint256 indexed streamId,
        uint256 totalDebt,
        UD21x18 oldRatePerSecond,
        UD21x18 newRatePerSecond
    );

    /// @notice Emitted when a stream is paused by the sender.
    /// @param streamId The ID of the stream.
    /// @param sender The stream's sender address.
    /// @param recipient The stream's recipient address.
    /// @param totalDebt The amount of tokens owed by the sender to the recipient, denoted in token's decimals.
    event PauseStream(
        uint256 indexed streamId,
        address indexed sender,
        address indexed recipient,
        uint256 totalDebt
    );

    /// @notice Emitted when a sender is refunded from a stream.
    /// @param streamId The ID of the stream.
    /// @param sender The stream's sender address.
    /// @param amount The amount of tokens refunded to the sender, denoted in token's decimals.
    event RefundFromStream(
        uint256 indexed streamId,
        address indexed sender,
        uint128 amount
    );

    /// @notice Emitted when a stream is restarted by the sender.
    /// @param streamId The ID of the stream.
    /// @param sender The stream's sender address.
    /// @param ratePerSecond The amount by which the debt is increasing every second, denoted as a fixed-point number
    /// where 1e18 is 1 token per second.
    event RestartStream(
        uint256 indexed streamId,
        address indexed sender,
        UD21x18 ratePerSecond
    );

    /// @notice Emitted when a stream is voided by the sender, recipient or an approved operator.
    /// @param streamId The ID of the stream.
    /// @param sender The stream's sender address.
    /// @param recipient The stream's recipient address.
    /// @param caller The address that performed the void, which can be the sender, recipient or an approved operator.
    /// @param newTotalDebt The new total debt, denoted in token's decimals.
    /// @param writtenOffDebt The amount of debt written off by the caller, denoted in token's decimals.
    event VoidStream(
        uint256 indexed streamId,
        address indexed sender,
        address indexed recipient,
        address caller,
        uint256 newTotalDebt,
        uint256 writtenOffDebt
    );

    event CollectProtocolRevenue(
        address indexed admin,
        IERC20 indexed token,
        address indexed to,
        uint128 revenue
    );

    event SetProtocolFee(
        address indexed admin,
        IERC20 indexed token,
        UD60x18 oldProtocolFee,
        UD60x18 newProtocolFee
    );

    event CollectNativeFees(address indexed admin, uint256 feeAmount);
}
