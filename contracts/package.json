{"name": "streamera-contracts", "version": "1.0.0", "description": "", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "create-stream": "tsx script/hedera_testnet/create-stream.ts", "deposit-token": "tsx script/hedera_testnet/deposit-token.ts", "deploy": "forge script script/DeployStreamera.s.sol --rpc-url hedera_testnet --broadcast --verify --verifier sourcify --verifier-url https://server-verify.hashscan.io", "test:frok": "forge test -vvv --fork-url hedera_testnet"}, "devDependencies": {"@types/node": "^24.3.0", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"dotenv": "^17.2.2", "ethers": "^6.15.0"}}