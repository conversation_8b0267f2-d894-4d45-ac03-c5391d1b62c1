// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {Streamera} from "../src/Streamera.sol";

contract DeployHashPay is Script {
    function run() public {
        uint256 pk = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(pk);

        Streamera streamera = new Streamera();

        vm.stopBroadcast();

        console.log("streamera deployed at:", address(streamera));
    }
}
