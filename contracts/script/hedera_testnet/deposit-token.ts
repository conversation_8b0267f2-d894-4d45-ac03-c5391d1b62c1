/**
 * Deposit to Stream Script for Streamera Contract
 *
 * This script deposits tokens to an existing stream using the Streamera contract.
 * The script has been updated to work with the new Streamera contract architecture.
 *
 * Note: Unlike the old HourGlassPayer contract, Streamera requires depositing to
 * specific streams (by streamId) rather than a general vault.
 */

import { ethers, formatUnits, parseUnits } from "ethers";
import { config } from "dotenv";
import { readFileSync } from "fs";
import { join } from "path";

// Load environment variables
config();

const erc20Abi = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function approve(address spender, uint256 amount) returns (bool)",
];

// Configuration from .env file
const privateKey = process.env.PRIVATE_KEY;
const rpcUrl = process.env.RPC_URL || "https://testnet.hashio.io/api";
const streameraContractAddress =
  process.env.STREAMERA_CONTRACT_ADDRESS ||
  process.env.PAYER_CONTRACT_ADDRESS ||
  "******************************************";
const tokenAddress =
  process.env.TOKEN_ADDRESS || "******************************************";
const depositAmountHumanStr = process.env.DEPOSIT_AMOUNT || "1"; // Amount to deposit in human normal decimals
const streamId = process.env.STREAM_ID || "0"; // Stream ID to deposit to

// Load the full ABI from the compiled contract
const abiPath = join(
  process.cwd(),
  "out",
  "Streamera.sol",
  "Streamera.json"
);
const contractJson = JSON.parse(readFileSync(abiPath, "utf8"));
const streameraAbi = contractJson.abi;

let depositAmount: bigint;

async function depositToStream() {
  try {
    // Validate private key
    if (!privateKey) {
      throw new Error("PRIVATE_KEY not found in .env file");
    }

    // Validate stream ID
    if (!streamId) {
      throw new Error("STREAM_ID not found in .env file");
    }

    // Create provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Create signer
    const signer = new ethers.Wallet(privateKey, provider);

    console.log("Signer address:", signer.address);

    // Create contract instance
    const streameraContract = new ethers.Contract(
      streameraContractAddress,
      streameraAbi,
      signer
    );

    // Get stream information first
    console.log("Getting stream information...");
    const stream = await streameraContract.getStream(streamId);
    const streamSender = stream.sender;
    const streamRecipient = await streameraContract.getStreamRecipient(streamId);

    console.log("Stream ID:", streamId);
    console.log("Stream sender:", streamSender);
    console.log("Stream recipient:", streamRecipient);
    console.log("Stream token:", stream.token);

    // Check current balance before deposit
    let tokenContract = new ethers.Contract(tokenAddress, erc20Abi, signer);

    const balance = await tokenContract.balanceOf(signer.address);
    const decimals = await tokenContract.decimals();
    const symbol = await tokenContract.symbol();

    console.log("Token Symbol:", symbol);
    console.log("Token Decimals:", decimals);

    depositAmount = parseUnits(depositAmountHumanStr, decimals);

    console.log(`Current balance: ${formatUnits(balance, decimals)} ${symbol}`);

    // Approve the Streamera contract to spend tokens
    console.log("Approving Streamera contract to spend tokens...");

    const approveTx = await tokenContract.approve(
      streameraContractAddress,
      depositAmount
    );
    await approveTx.wait();
    console.log("Approval confirmed");

    console.log("Depositing tokens with parameters:");
    console.log("Stream ID:", streamId);
    console.log("Token:", tokenAddress);
    console.log("Amount:", depositAmount.toString());
    console.log("Sender:", streamSender);
    console.log("Recipient:", streamRecipient);
    console.log("Depositor:", signer.address);

    // Call deposit function - note: amount needs to be uint128
    console.log("Depositing tokens to stream...");
    const tx = await streameraContract.deposit(
      streamId,
      depositAmount,
      streamSender,
      streamRecipient
    );

    console.log("Transaction sent:", tx.hash);

    // Wait for transaction confirmation
    const receipt = await tx.wait();
    console.log("Transaction confirmed in block:", receipt.blockNumber);

    // Get the deposit event from the transaction logs
    const depositEvent = receipt.logs.find((log: any) => {
      try {
        const parsed = streameraContract.interface.parseLog(log);
        return parsed && parsed.name === "DepositToStream";
      } catch {
        return false;
      }
    });

    if (depositEvent) {
      const parsedEvent = streameraContract.interface.parseLog(depositEvent);
      if (parsedEvent) {
        console.log("Deposit successful!");
        console.log("Stream ID:", parsedEvent.args.streamId.toString());
        console.log("Funder:", parsedEvent.args.funder);
        console.log("Amount:", parsedEvent.args.amount.toString());
      }
    }

    // Check updated balance after deposit
    if (tokenContract) {
      const newBalance = await tokenContract.balanceOf(signer.address);
      const decimals = await tokenContract.decimals();
      const symbol = await tokenContract.symbol();
      console.log(
        `Updated balance: ${ethers.formatUnits(newBalance, decimals)} ${symbol}`
      );
    }

    // Check updated stream balance
    const updatedStream = await streameraContract.getStream(streamId);
    console.log(`Updated stream balance: ${updatedStream.balance.toString()}`);

  } catch (error) {
    console.error("Error depositing tokens to stream:", error);
  }
}

// Run the function
depositToStream();

// Instructions:
// 1. Update your .env file with your private key and stream details
// 2. Set STREAM_ID to the ID of the stream you want to deposit to
// 3. Set DEPOSIT_AMOUNT to the amount you want to deposit (in human-readable decimals)
// 4. Run: npm run ts-node script/hedera_testnet/deposit-token.ts
//
// Required .env variables:
// - PRIVATE_KEY: Your wallet private key
// - STREAM_ID: ID of the stream to deposit to
// - TOKEN_ADDRESS: Token to deposit
// - DEPOSIT_AMOUNT: Amount to deposit (in human-readable decimals)
// - STREAMERA_CONTRACT_ADDRESS: Streamera contract address
