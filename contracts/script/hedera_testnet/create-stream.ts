import { ethers } from "ethers";
import { config } from "dotenv";
import { readFileSync } from "fs";
import { join } from "path";

// Load environment variables
config();

// Configuration from .env file
const privateKey = process.env.PRIVATE_KEY;
const rpcUrl = process.env.RPC_URL || "https://testnet.hashio.io/api";
const streameraContractAddress =
  process.env.STREAMERA_CONTRACT_ADDRESS ||
  "******************************************";
const tokenAddress =
  process.env.TOKEN_ADDRESS || "******************************************";
const recipientAddress =
  process.env.RECIPIENT_ADDRESS || "******************************************";
// Rate per second in UD21x18 format (1e18 = 1 token per second)
// For example: "1000000000000000000" = 1 token per second
const ratePerSecondStr =
  process.env.RATE_PER_SECOND ||
  process.env.AMOUNT_PER_SEC ||
  "100000000000000000"; // 0.1 tokens per second 
// Amount to deposit into the stream (should be enough to cover the streaming duration)
const depositAmountStr = "1"; 
// Whether the stream NFT is transferable
const transferable = true;

// Gas configuration
const gasLimit = process.env.GAS_LIMIT
  ? parseInt(process.env.GAS_LIMIT)
  : 1_000_000; // Increased default gas for Streamera contract

// Load the full ABI from the compiled contract
const abiPath = join(process.cwd(), "out", "Streamera.sol", "Streamera.json");

const contractJson = JSON.parse(readFileSync(abiPath, "utf8"));
const streameraAbi = contractJson.abi;

async function createStream() {
  try {
    // Validate private key
    if (!privateKey) {
      throw new Error("PRIVATE_KEY not found in .env file");
    }

    // Create provider
    const provider = new ethers.JsonRpcProvider(rpcUrl);

    // Create signer
    const signer = new ethers.Wallet(privateKey, provider);

    console.log("Signer address:", signer.address);

    // Create contract instance
    const streameraContract = new ethers.Contract(
      streameraContractAddress,
      streameraAbi,
      signer
    );

    // Stream parameters from .env
    const ratePerSecond = BigInt(ratePerSecondStr);
    // const depositAmount = BigInt(depositAmountStr);

    console.log("Creating stream with parameters:");
    console.log("Streamera Contract:", streameraContractAddress);
    console.log("Token:", tokenAddress);
    console.log("Sender:", signer.address);
    console.log("Recipient:", recipientAddress);
    console.log(
      "Rate per second:",
      ratePerSecond.toString(),
      "(UD21x18 format)"
    );
    console.log("Transferable:", transferable);

    // Calculate streaming duration based on deposit amount and rate
    // const streamingDurationSeconds = Number(depositAmount) / Number(ratePerSecond);
    // console.log("Estimated streaming duration:", streamingDurationSeconds, "seconds");
    // console.log("Estimated streaming duration:", (streamingDurationSeconds / 3600).toFixed(2), "hours");

    // Call createAndDeposit function with manual gas settings
    console.log("\n=== Gas Configuration ===");
    console.log(`Gas Limit: ${gasLimit.toLocaleString()} gas units`);

    console.log("========================\n");

    // Prepare transaction options
    const txOptions: any = {
      gasLimit: gasLimit,
    };

    console.log("Sending create transaction...");

    // Use create to create stream and fund it in one transaction
    const tx = await streameraContract.create(
      signer.address, // sender
      recipientAddress, // recipient
      ratePerSecond, // ratePerSecond (UD21x18)
      tokenAddress, // token
      transferable, // transferable
      txOptions
    );

    console.log("Transaction sent:", tx.hash);

    // Wait for transaction confirmation
    const receipt = await tx.wait();
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log(
      `Gas used: ${receipt.gasUsed.toString()} / ${gasLimit.toString()} (limit)`
    );
    console.log(
      `Gas efficiency: ${(
        (Number(receipt.gasUsed) / Number(gasLimit)) *
        100
      ).toFixed(2)}%`
    );

    // Get the stream ID from the transaction logs
    const createStreamEvent = receipt.logs.find((log: any) => {
      try {
        const parsed = streameraContract.interface.parseLog(log);
        return parsed && parsed.name === "StreamCreated";
      } catch {
        return false;
      }
    });

    if (createStreamEvent) {
      const parsedEvent =
        streameraContract.interface.parseLog(createStreamEvent);
      if (parsedEvent) {
        console.log(
          "Stream created with ID:",
          parsedEvent.args.streamId.toString()
        );
        console.log("Stream sender:", parsedEvent.args.sender);
        console.log("Stream recipient:", parsedEvent.args.recipient);
        console.log(
          "Stream rate per second:",
          parsedEvent.args.ratePerSecond.toString()
        );
        console.log("Stream token:", parsedEvent.args.token);
        console.log("Stream transferable:", parsedEvent.args.transferable);
      }
    }

    // // Also look for DepositToStream event
    // const depositEvent = receipt.logs.find((log: any) => {
    //   try {
    //     const parsed = streameraContract.interface.parseLog(log);
    //     return parsed && parsed.name === "DepositToStream";
    //   } catch {
    //     return false;
    //   }
    // });

    // if (depositEvent) {
    //   const parsedEvent = streameraContract.interface.parseLog(depositEvent);
    //   if (parsedEvent) {
    //     console.log("Deposit made to stream ID:", parsedEvent.args.streamId.toString());
    //     console.log("Deposit amount:", parsedEvent.args.amount.toString());
    //     console.log("Deposit funder:", parsedEvent.args.funder);
    //   }
    // }
  } catch (error) {
    console.error("Error creating stream:", error);
  }
}

// Run the function
createStream();
