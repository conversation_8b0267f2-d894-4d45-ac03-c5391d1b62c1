// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {IHederaTokenService} from "../../src/interfaces/IHederaTokenService.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {SafeCast} from "@openzeppelin/contracts/utils/math/SafeCast.sol";
import {Test} from "forge-std/Test.sol";

contract MockHederaTokenService is Test {
    address constant PRECOMPILE = address(0x167);

    // Mock the Hedera Token Service precompile
    function transferToken(
        address token,
        address sender,
        address receiver,
        int64 amount
    ) external returns (int32) {
        // For testing, directly modify balances using vm.store
        require(amount >= 0, "Negative amount not supported");
        uint256 amountUint = uint256(uint64(amount));

        // Get current balances
        uint256 senderBalance = ERC20(token).balanceOf(sender);
        uint256 receiverBalance = ERC20(token).balanceOf(receiver);

        // Update balances directly (ERC20 _balances mapping is at slot 0)
        vm.store(
            token,
            keccak256(abi.encode(sender, uint256(0))),
            bytes32(senderBalance - amountUint)
        );
        vm.store(
            token,
            keccak256(abi.encode(receiver, uint256(0))),
            bytes32(receiverBalance + amountUint)
        );

        return 22; // SUCCESS response code
    }

    function approve(
        address token,
        address spender,
        int64 amount
    ) external returns (int32) {
        // Convert int64 to uint256 safely
        require(amount >= 0, "Negative amount not supported");
        ERC20(token).approve(spender, uint256(uint64(amount)));
        return 22; // SUCCESS response code
    }

    // function associateToken(
    //     address /*account*/,
    //     address /*token*/
    // ) external pure returns (int32) {
    //     // In test environment, association is not needed
    //     return 22; // SUCCESS response code
    // }

    function associateToken(
        address account,
        address token,
        int32 code
    ) external {
        vm.mockCall(
            PRECOMPILE,
            abi.encodeWithSelector(
                IHederaTokenService.associateToken.selector,
                account,
                token
            ),
            abi.encode(code)
        );
    }
}
