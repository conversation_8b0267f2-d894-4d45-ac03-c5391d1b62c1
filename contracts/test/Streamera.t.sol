// SPDX-License-Identifier: MIT
pragma solidity ^0.8.30;

import {Test} from "forge-std/Test.sol";
import {console} from "forge-std/console.sol";
import {Streamera} from "../src/Streamera.sol";
import {UD21x18} from "@prb/math/src/UD21x18.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {MockHederaTokenService} from "./mocks/MockHederaTokenService.sol";
import {IERC20Metadata} from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

contract StreameraTest is Test {
    Streamera public streamera;
    MockHederaTokenService public mockHederaTokenService;
    address public constant USDC_TOKEN =
        0x0000000000000000000000000000000000001549;

    address public constant RECIPIENT =
        address(0x2619Ee9DDE7EBd1cce9F427E558D8e9B333d75a7);

    function setUp() public {
        // Deploy the contracts
        streamera = new Streamera();
        mockHederaTokenService = new MockHederaTokenService();

        // Mock the token association
        mockHederaTokenService.associateToken(
            address(streamera),
            USDC_TOKEN,
            22
        );

        // Mock ERC20 metadata for USDC
        vm.mockCall(
            USDC_TOKEN,
            abi.encodeWithSelector(IERC20Metadata.decimals.selector),
            abi.encode(uint8(6)) // USDC normally has 6 decimals
        );

        vm.mockCall(
            USDC_TOKEN,
            abi.encodeWithSelector(IERC20Metadata.symbol.selector),
            abi.encode("USDC")
        );

        vm.mockCall(
            USDC_TOKEN,
            abi.encodeWithSelector(IERC20Metadata.name.selector),
            abi.encode("USD Coin")
        );
    }

    function testCreateStream() public {
        // Create a stream
        streamera.create(
            address(this),
            RECIPIENT,
            UD21x18.wrap(1e18),
            IERC20(USDC_TOKEN),
            false
        );

        // Check the stream was created
        assertEq(streamera.nextStreamId(), 1);
    }
}
