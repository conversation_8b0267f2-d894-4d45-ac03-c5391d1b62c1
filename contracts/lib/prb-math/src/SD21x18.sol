// SPDX-License-Identifier: MIT
pragma solidity >=0.8.19;

/*

██████╗ ██████╗ ██████╗ ███╗   ███╗ █████╗ ████████╗██╗  ██╗
██╔══██╗██╔══██╗██╔══██╗████╗ ████║██╔══██╗╚══██╔══╝██║  ██║
██████╔╝██████╔╝██████╔╝██╔████╔██║███████║   ██║   ███████║
██╔═══╝ ██╔══██╗██╔══██╗██║╚██╔╝██║██╔══██║   ██║   ██╔══██║
██║     ██║  ██║██████╔╝██║ ╚═╝ ██║██║  ██║   ██║   ██║  ██║
╚═╝     ╚═╝  ╚═╝╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝

███████╗██████╗ ██████╗  ██╗██╗  ██╗ ██╗ █████╗
██╔════╝██╔══██╗╚════██╗███║╚██╗██╔╝███║██╔══██╗
███████╗██║  ██║ █████╔╝╚██║ ╚███╔╝ ╚██║╚█████╔╝
╚════██║██║  ██║██╔═══╝  ██║ ██╔██╗  ██║██╔══██╗
███████║██████╔╝███████╗ ██║██╔╝ ██╗ ██║╚█████╔╝
╚══════╝╚═════╝ ╚══════╝ ╚═╝╚═╝  ╚═╝ ╚═╝ ╚════╝

*/

import "./sd21x18/Casting.sol";
import "./sd21x18/Constants.sol";
import "./sd21x18/Errors.sol";
import "./sd21x18/ValueType.sol";
