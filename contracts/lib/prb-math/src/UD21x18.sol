// SPDX-License-Identifier: MIT
pragma solidity >=0.8.19;

/*

██████╗ ██████╗ ██████╗ ███╗   ███╗ █████╗ ████████╗██╗  ██╗
██╔══██╗██╔══██╗██╔══██╗████╗ ████║██╔══██╗╚══██╔══╝██║  ██║
██████╔╝██████╔╝██████╔╝██╔████╔██║███████║   ██║   ███████║
██╔═══╝ ██╔══██╗██╔══██╗██║╚██╔╝██║██╔══██║   ██║   ██╔══██║
██║     ██║  ██║██████╔╝██║ ╚═╝ ██║██║  ██║   ██║   ██║  ██║
╚═╝     ╚═╝  ╚═╝╚═════╝ ╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝

██╗   ██╗██████╗ ██████╗  ██╗██╗  ██╗ ██╗ █████╗ 
██║   ██║██╔══██╗╚════██╗███║╚██╗██╔╝███║██╔══██╗
██║   ██║██║  ██║ █████╔╝╚██║ ╚███╔╝ ╚██║╚█████╔╝
██║   ██║██║  ██║██╔═══╝  ██║ ██╔██╗  ██║██╔══██╗
╚██████╔╝██████╔╝███████╗ ██║██╔╝ ██╗ ██║╚█████╔╝
 ╚═════╝ ╚═════╝ ╚══════╝ ╚═╝╚═╝  ╚═╝ ╚═╝ ╚════╝
 
*/

import "./ud21x18/Casting.sol";
import "./ud21x18/Constants.sol";
import "./ud21x18/Errors.sol";
import "./ud21x18/ValueType.sol";
