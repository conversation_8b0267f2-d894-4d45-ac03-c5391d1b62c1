name: "Sync"

on:
  push:
    tags:
      - "v4.x"

jobs:
  sync-release-branch:
    runs-on: "ubuntu-latest"
    if: "startsWith(github.ref, 'refs/tags/v4.')"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"
        with:
          fetch-depth: 0
          ref: "release-v4"

      - name: "Configure Git"
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com

      - name: "Sync Release Branch"
        run: |
          git fetch --tags
          git checkout release-v4
          git reset --hard ${GITHUB_REF}
          git push --force
