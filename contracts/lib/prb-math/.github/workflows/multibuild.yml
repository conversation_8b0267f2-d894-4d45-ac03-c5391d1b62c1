name: "Multibuild"

on:
  workflow_dispatch:
  schedule:
    - cron: "0 3 * * 0" # at 3:00am UTC every Sunday

jobs:
  multibuild:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - name: "Install Bun"
        uses: "oven-sh/setup-bun@v1"

      - name: "Install the Node.js dependencies"
        run: "bun install"

      - name: "Check that PRBMath can be built with multiple Solidity versions"
        uses: "PaulRBerg/foundry-multibuild@v1"
        with:
          min: "0.8.19"
          max: "0.8.26"
          skip-test: "true"
