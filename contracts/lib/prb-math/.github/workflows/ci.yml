name: "CI"

concurrency:
  cancel-in-progress: true
  group: ${{github.workflow}}-${{github.ref}}

env:
  FOUNDRY_PROFILE: "ci"

on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - "main"
      - "staging"

jobs:
  lint:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Install Bun"
        uses: "oven-sh/setup-bun@v1"

      - name: "Install the Node.js dependencies"
        run: "bun install"

      - name: "Lint the code"
        run: "bun run lint"

      - name: "Add lint summary"
        run: |
          echo "## Lint result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  build:
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - name: "Install Bun"
        uses: "oven-sh/setup-bun@v1"

      - name: "Install the Node.js dependencies"
        run: "bun install"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Build the Solidity code"
        run: "forge build"

      - name: "Add build summary"
        run: |
          echo "## Build result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY

  test:
    needs: ["lint", "build"]
    runs-on: "ubuntu-latest"
    steps:
      - name: "Check out the repo"
        uses: "actions/checkout@v4"

      - name: "Install Foundry"
        uses: "foundry-rs/foundry-toolchain@v1"

      - name: "Install Bun"
        uses: "oven-sh/setup-bun@v1"

      - name: "Install the Node.js dependencies"
        run: "bun install"

      - name: "Run the tests"
        run: "forge test"

      - name: "Add test summary"
        run: |
          echo "## Tests result" >> $GITHUB_STEP_SUMMARY
          echo "✅ Passed" >> $GITHUB_STEP_SUMMARY
