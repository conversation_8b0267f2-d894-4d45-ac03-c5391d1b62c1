{"name": "@prb/math", "description": "Solidity library for advanced fixed-point math", "version": "4.1.0", "author": {"name": "<PERSON>", "url": "https://github.com/PaulRBerg"}, "bugs": {"url": "https://github.com/PaulRBerg/prb-math/issues"}, "devDependencies": {"forge-std": "github:foundry-rs/forge-std#v1.8.2", "prettier": "^3.3.2", "solhint": "^5.0.1"}, "files": ["src", "test/utils", "CHANGELOG.md"], "homepage": "https://github.com/PaulRBerg/prb-math#readme", "keywords": ["arithmetic", "blockchain", "ethereum", "fixed-point", "fixed-point-math", "library", "math", "smart-contracts", "solidity"], "license": "MIT", "publishConfig": {"access": "public"}, "repository": "github:<PERSON>/prb-math", "scripts": {"build": "forge build", "clean": "rm -rf cache out", "lint": "bun run lint:sol && bun run prettier:check", "lint:sol": "forge fmt --check && bun solhint \"{src,test}/**/*.sol\"", "prettier:check": "prettier --check \"**/*.{json,md,yml}\"", "prettier:write": "prettier --write \"**/*.{json,md,yml}\"", "test": "forge test"}}